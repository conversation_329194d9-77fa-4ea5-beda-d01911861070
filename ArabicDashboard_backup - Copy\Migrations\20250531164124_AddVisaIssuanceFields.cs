﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArabicDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddVisaIssuanceFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ChamberOfCommerceFees",
                table: "SharedAccounts",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "ClientName",
                table: "SharedAccounts",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "EmploymentFees",
                table: "SharedAccounts",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "ForeignAffairsFees",
                table: "SharedAccounts",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "GovernmentFees",
                table: "SharedAccounts",
                type: "TEXT",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "ServiceCategory",
                table: "SharedAccounts",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "VisaType",
                table: "SharedAccounts",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChamberOfCommerceFees",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "ClientName",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "EmploymentFees",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "ForeignAffairsFees",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "GovernmentFees",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "ServiceCategory",
                table: "SharedAccounts");

            migrationBuilder.DropColumn(
                name: "VisaType",
                table: "SharedAccounts");
        }
    }
}
