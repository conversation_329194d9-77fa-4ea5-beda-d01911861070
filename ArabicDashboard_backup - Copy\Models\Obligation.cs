using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    /// <summary>
    /// نموذج الالتزامات
    /// </summary>
    public class Obligation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = "";

        [Required]
        [MaxLength(100)]
        public string Type { get; set; } = "";

        [Required]
        [MaxLength(100)]
        public string Duration { get; set; } = "";

        [Required]
        public DateTime DueDate { get; set; }

        public bool IsReminderEnabled { get; set; } = true;

        public int ReminderDaysBefore { get; set; } = 7;

        [MaxLength(500)]
        public string Notes { get; set; } = "";

        public decimal Amount { get; set; } = 0;

        [MaxLength(50)]
        public string Status { get; set; } = "نشط";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        [MaxLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        // خصائص محسوبة
        public int DaysRemaining
        {
            get
            {
                var days = (DueDate - DateTime.Now).Days;
                return days < 0 ? 0 : days;
            }
        }

        public bool IsOverdue => DateTime.Now > DueDate && Status == "نشط";

        public bool IsNearDue => DaysRemaining <= ReminderDaysBefore && DaysRemaining > 0 && Status == "نشط";

        public string StatusColor
        {
            get
            {
                if (IsOverdue) return "#E53E3E"; // أحمر
                if (IsNearDue) return "#F59E0B"; // برتقالي
                return "#10B981"; // أخضر
            }
        }

        public string StatusText
        {
            get
            {
                if (IsOverdue) return "متأخر";
                if (IsNearDue) return "قريب الاستحقاق";
                return Status;
            }
        }
    }
}
