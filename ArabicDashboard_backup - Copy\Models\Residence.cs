using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    public class Residence
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string WorkerName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ResidenceNumber { get; set; } = string.Empty;

        [Required]
        public DateTime RenewalDate { get; set; }

        [Required]
        public DateTime ExpiryDate { get; set; }

        public DateTime? MedicalInsuranceExpiryDate { get; set; }

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // خصائص محسوبة
        public int DaysUntilExpiry
        {
            get
            {
                return (ExpiryDate.Date - DateTime.Today).Days;
            }
        }

        public bool IsExpired
        {
            get
            {
                return DaysUntilExpiry < 0;
            }
        }

        public bool IsExpiringSoon
        {
            get
            {
                return DaysUntilExpiry <= 30 && DaysUntilExpiry > 0;
            }
        }

        public string Status
        {
            get
            {
                if (IsExpired)
                    return "منتهية";
                else if (IsExpiringSoon)
                    return "قاربت على الانتهاء";
                else
                    return "سارية";
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "سارية" => "#10B981", // أخضر
                    "قاربت على الانتهاء" => "#F59E0B", // برتقالي
                    "منتهية" => "#EF4444", // أحمر
                    _ => "#6B7280" // رمادي
                };
            }
        }

        // خصائص التأمين الطبي
        public int DaysUntilMedicalInsuranceExpiry
        {
            get
            {
                if (!MedicalInsuranceExpiryDate.HasValue) return int.MaxValue;
                return (MedicalInsuranceExpiryDate.Value.Date - DateTime.Today).Days;
            }
        }

        public bool IsMedicalInsuranceExpired
        {
            get
            {
                return MedicalInsuranceExpiryDate.HasValue && DaysUntilMedicalInsuranceExpiry < 0;
            }
        }

        public bool IsMedicalInsuranceExpiringSoon
        {
            get
            {
                return MedicalInsuranceExpiryDate.HasValue && DaysUntilMedicalInsuranceExpiry <= 30 && DaysUntilMedicalInsuranceExpiry > 0;
            }
        }

        public string MedicalInsuranceStatus
        {
            get
            {
                if (!MedicalInsuranceExpiryDate.HasValue)
                    return "غير محدد";
                else if (IsMedicalInsuranceExpired)
                    return "منتهي";
                else if (IsMedicalInsuranceExpiringSoon)
                    return "قارب ينتهي";
                else
                    return "ساري";
            }
        }
    }
}
