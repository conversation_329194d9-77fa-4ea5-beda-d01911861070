using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArabicDashboard.Models
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string ClientName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(300)]
        public string ClientAddress { get; set; } = string.Empty;

        [StringLength(50)]
        public string ClientIdNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string TransactionType { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount => Amount - PaidAmount;

        [StringLength(50)]
        public string Status { get; set; } = "غير مُسدد";

        public DateTime IssueDate { get; set; } = DateTime.Now;

        public DateTime? DueDate { get; set; }

        public DateTime? PaymentDate { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        // خصائص جديدة للفاتورة الإلكترونية
        [StringLength(20)]
        public string InvoiceType { get; set; } = "فاتورة"; // فاتورة أو سند

        public bool IsElectronic { get; set; } = true;

        [StringLength(100)]
        public string QRCode { get; set; } = string.Empty;

        [StringLength(50)]
        public string TaxNumber { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal => Amount - TaxAmount;

        // معلومات الطباعة والتصدير
        public DateTime? PrintedDate { get; set; }
        public int PrintCount { get; set; } = 0;
        public DateTime? ExportedDate { get; set; }

        // حالة الفاتورة
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;

        [StringLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        // خصائص محسوبة للعرض
        public string StatusText => Status;

        public string StatusColor => Status switch
        {
            "مُسدد" => "#10B981", // أخضر
            "مُسدد جزئياً" => "#F59E0B", // برتقالي
            "غير مُسدد" => "#EF4444", // أحمر
            _ => "#6B7280" // رمادي
        };

        public string StampText => Status;

        public string AmountFormatted => Amount.ToString("N0") + " ريال";

        public string PaidAmountFormatted => PaidAmount.ToString("N0") + " ريال";

        public string RemainingAmountFormatted => RemainingAmount.ToString("N0") + " ريال";

        public string IssueDateFormatted => IssueDate.ToString("yyyy/MM/dd");

        public string DueDateFormatted => DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";

        public string PaymentDateFormatted => PaymentDate?.ToString("yyyy/MM/dd") ?? "لم يتم الدفع";

        public string ElectronicText => IsElectronic ?
            (InvoiceType == "فاتورة" ? "فاتورة إلكترونية - لا تحتاج إلى توقيع" : "سند إلكتروني - لا يحتاج إلى توقيع") : "";
    }

    public enum PaymentStatus
    {
        Unpaid = 0,      // غير مُسدد
        PartiallyPaid = 1, // مُسدد جزئياً
        Paid = 2         // مُسدد
    }

    // نموذج عناصر الفاتورة
    public class InvoiceItem
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int InvoiceId { get; set; }

        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;

        [Required]
        [StringLength(300)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int Quantity { get; set; } = 1;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice => Quantity * UnitPrice;

        [StringLength(100)]
        public string Unit { get; set; } = "خدمة";
    }

    // نموذج السند
    public class Receipt : Invoice
    {
        [StringLength(100)]
        public string ReceiptFor { get; set; } = string.Empty; // لصالح

        [StringLength(200)]
        public string PaymentMethod { get; set; } = "نقداً"; // طريقة الدفع

        [StringLength(100)]
        public string ReceivedBy { get; set; } = string.Empty; // استلمه

        public bool RequiresSignature { get; set; } = false; // يحتاج توقيع

        public Receipt()
        {
            InvoiceType = "سند";
            IsElectronic = true;
        }
    }
}
