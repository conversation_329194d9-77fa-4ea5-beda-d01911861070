﻿#pragma checksum "..\..\..\ArchiveWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "91F0B853A89B2A824F61D24F2BCD064A525926C6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Bars;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using DevExpress.Xpf.LayoutControl;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// ArchiveWindow
    /// </summary>
    public partial class ArchiveWindow : DevExpress.Xpf.Core.ThemedWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManualArchive;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalArchives;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNextArchive;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalRecords;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSystemStatus;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSystemStatusText;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateFrom;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateTo;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbArchiveType;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilter;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridArchives;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatusMessage;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewArchive;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportArchive;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\ArchiveWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteArchive;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/archivewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ArchiveWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnManualArchive = ((System.Windows.Controls.Button)(target));
            return;
            case 2:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.TxtTotalArchives = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtNextArchive = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtTotalRecords = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtSystemStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtSystemStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtSearch = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 11:
            this.DateFrom = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 12:
            this.DateTo = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 13:
            this.CmbArchiveType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            return;
            case 14:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            return;
            case 15:
            this.BtnClearFilter = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.GridArchives = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 17:
            this.TxtStatusMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.BtnViewArchive = ((System.Windows.Controls.Button)(target));
            return;
            case 19:
            this.BtnExportArchive = ((System.Windows.Controls.Button)(target));
            return;
            case 20:
            this.BtnDeleteArchive = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

