using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    public class Worker
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string WorkerName { get; set; } = "";

        [Required]
        [MaxLength(50)]
        public string ResidenceNumber { get; set; } = "";

        public DateTime EntryDate { get; set; } = DateTime.Now;

        public DateTime SubscriptionEndDate { get; set; } = DateTime.Now.AddYears(1);

        [Required]
        [MaxLength(200)]
        public string SponsorName { get; set; } = "";

        [Required]
        [MaxLength(50)]
        public string SponsorIdNumber { get; set; } = "";

        [MaxLength(500)]
        public string SponsorIdImagePath { get; set; } = "";

        [MaxLength(500)]
        public string CommercialRegistrationPath { get; set; } = "";

        // بيانات قوى
        [MaxLength(100)]
        public string QawaUsername { get; set; } = "";

        [MaxLength(100)]
        public string QawaPassword { get; set; } = "";

        // بيانات أبشر
        [MaxLength(100)]
        public string AbsherUsername { get; set; } = "";

        [MaxLength(100)]
        public string AbsherPassword { get; set; } = "";

        // بيانات الغرفة التجارية
        [MaxLength(100)]
        public string ChamberUsername { get; set; } = "";

        [MaxLength(100)]
        public string ChamberPassword { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [MaxLength(1000)]
        public string Notes { get; set; } = "";
        
        // خصائص محسوبة
        public int RemainingDays
        {
            get
            {
                var remaining = (SubscriptionEndDate - DateTime.Now).Days;
                return Math.Max(0, remaining);
            }
        }

        public string Status
        {
            get
            {
                var remainingDays = RemainingDays;
                if (remainingDays <= 0)
                    return "منتهي";
                else if (remainingDays <= 30)
                    return "قارب ينتهي";
                else
                    return "اشتراك ساري";
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    "اشتراك ساري" => "#10B981", // أخضر
                    "قارب ينتهي" => "#F59E0B", // برتقالي
                    "منتهي" => "#EF4444", // أحمر
                    _ => "#6B7280" // رمادي
                };
            }
        }
    }
}
