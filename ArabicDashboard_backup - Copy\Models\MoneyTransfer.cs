using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    public class MoneyTransfer
    {
        public int Id { get; set; }
        
        [Required]
        public string TransferNumber { get; set; } = string.Empty;
        
        [Required]
        public string SenderName { get; set; } = string.Empty;
        
        [Required]
        public string SenderPhone { get; set; } = string.Empty;
        
        [Required]
        public string ReceiverName { get; set; } = string.Empty;
        
        [Required]
        public string ReceiverPhone { get; set; } = string.Empty;
        
        public string ReceiverCountry { get; set; } = string.Empty;
        
        public string ReceiverCity { get; set; } = string.Empty;
        
        [Required]
        public decimal Amount { get; set; }
        
        public decimal ServiceFeeRate { get; set; } = 0.05m; // 5%

        public decimal TaxRate { get; set; } = 0.15m; // 15%

        // خيارات تفعيل الحسم والضريبة
        public bool ServiceFeeEnabled { get; set; } = true;

        public bool TaxEnabled { get; set; } = true;

        public decimal ServiceFeeAmount { get; set; }

        public decimal TaxAmount { get; set; }

        // حساب الرسوم والضرائب من المبلغ الإجمالي
        public decimal ServiceFee => ServiceFeeEnabled ? Amount * ServiceFeeRate : 0;

        public decimal Tax => TaxEnabled ? Amount * TaxRate : 0;

        public decimal TotalDeductions => ServiceFee + Tax;

        public decimal NetAmount => Amount - TotalDeductions;

        public decimal TotalAmount { get; set; } // المبلغ الإجمالي
        
        public decimal PaidAmount { get; set; }
        
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        
        public PaymentStatus Status { get; set; }
        
        public DateTime TransferDate { get; set; } = DateTime.Now;
        
        public DateTime? CompletionDate { get; set; }
        
        public string Notes { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedDate { get; set; }
        
        // خصائص محسوبة للعرض
        public string AmountFormatted => $"{Amount:N2} ريال";

        public string ServiceFeeFormatted => $"{ServiceFee:N2} ريال";

        public string TaxFormatted => $"{Tax:N2} ريال";

        public string TotalDeductionsFormatted => $"{TotalDeductions:N2} ريال";

        public string NetAmountFormatted => $"{NetAmount:N2} ريال";

        public string TotalAmountFormatted => $"{TotalAmount:N2} ريال";
        
        public string PaidAmountFormatted => $"{PaidAmount:N2} ريال";
        
        public string RemainingAmountFormatted => $"{RemainingAmount:N2} ريال";
        
        public string TransferDateFormatted => TransferDate.ToString("yyyy/MM/dd");
        
        public string StatusText => Status switch
        {
            PaymentStatus.Paid => "مُكتملة",
            PaymentStatus.PartiallyPaid => "مُسددة جزئياً",
            PaymentStatus.Unpaid => "في الانتظار",
            _ => "غير محدد"
        };
        
        public string StatusColor => Status switch
        {
            PaymentStatus.Paid => "#10B981",
            PaymentStatus.PartiallyPaid => "#F59E0B",
            PaymentStatus.Unpaid => "#EF4444",
            _ => "#6B7280"
        };
    }
}
